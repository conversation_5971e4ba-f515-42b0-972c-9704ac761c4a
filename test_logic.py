#!/usr/bin/env python3
"""
Test script for the AspectRatioLatentImage node logic (without PyTorch dependency).
This script tests the calculation logic to ensure the node works correctly.
"""

import math

class MockAspectRatioLatentImage:
    """Mock version of AspectRatioLatentImage for testing logic without PyTorch."""

    ASPECT_RATIOS = {
        "1:1 (Square)": 1.0,
        "4:3 (Standard)": 4/3,
        "3:2 (Classic)": 3/2,
        "16:9 (Widescreen)": 16/9,
        "16:10 (Wide)": 16/10,
        "21:9 (Ultrawide)": 21/9,
        "9:16 (Portrait)": 9/16,
        "3:4 (Portrait)": 3/4,
        "2:3 (Portrait)": 2/3,
        "10:16 (Portrait)": 10/16,
        "9:21 (Tall Portrait)": 9/21,
        "5:4 (Classic)": 5/4,
        "4:5 (Portrait)": 4/5,
        "Custom": None
    }

    def _round_to_multiple(self, value, multiple):
        """Round a value to the nearest multiple."""
        return int(round(value / multiple) * multiple)

    def _calculate_from_total_pixels(self, total_pixels, ratio):
        """Calculate width and height from total pixels and aspect ratio."""
        width = math.sqrt(total_pixels * ratio)
        height = math.sqrt(total_pixels / ratio)

        # Round to multiples of 8
        width = self._round_to_multiple(width, 8)
        height = self._round_to_multiple(height, 8)

        return width, height

    def calculate_dimensions(self, aspect_ratio, dimension_mode, width, height, total_pixels):
        """Calculate dimensions based on aspect ratio and mode."""

        # Get the aspect ratio value
        ratio = self.ASPECT_RATIOS.get(aspect_ratio)

        # Calculate final dimensions based on mode
        if aspect_ratio == "Custom":
            # Custom mode: use provided width and height as-is
            final_width = self._round_to_multiple(width, 8)
            final_height = self._round_to_multiple(height, 8)
        else:
            # Aspect ratio mode: calculate based on selected mode
            if dimension_mode == "Width Priority":
                final_width = self._round_to_multiple(width, 8)
                final_height = self._round_to_multiple(int(final_width / ratio), 8)
            elif dimension_mode == "Height Priority":
                final_height = self._round_to_multiple(height, 8)
                final_width = self._round_to_multiple(int(final_height * ratio), 8)
            else:  # Total Pixels mode
                final_width, final_height = self._calculate_from_total_pixels(total_pixels, ratio)

        # Ensure dimensions are within bounds
        MAX_RESOLUTION = 16384
        final_width = max(64, min(final_width, MAX_RESOLUTION))
        final_height = max(64, min(final_height, MAX_RESOLUTION))

        return final_width, final_height

def test_calculations():
    """Test the dimension calculation logic."""

    print("Testing AspectRatioLatentImage Calculation Logic")
    print("=" * 55)

    node = MockAspectRatioLatentImage()

    # Test cases: (aspect_ratio, dimension_mode, width, height, total_pixels, expected_description)
    test_cases = [
        # Width Priority tests
        ("16:9 (Widescreen)", "Width Priority", 1024, 576, 1048576, "16:9 with width 1024"),
        ("4:3 (Standard)", "Width Priority", 800, 600, 1048576, "4:3 with width 800"),
        ("1:1 (Square)", "Width Priority", 512, 512, 1048576, "1:1 with width 512"),
        ("9:16 (Portrait)", "Width Priority", 576, 1024, 1048576, "9:16 portrait with width 576"),

        # Height Priority tests
        ("16:9 (Widescreen)", "Height Priority", 1024, 576, 1048576, "16:9 with height 576"),
        ("4:3 (Standard)", "Height Priority", 800, 600, 1048576, "4:3 with height 600"),
        ("9:16 (Portrait)", "Height Priority", 576, 1024, 1048576, "9:16 portrait with height 1024"),

        # Total Pixels tests
        ("16:9 (Widescreen)", "Total Pixels", 1024, 576, 1048576, "16:9 with 1M pixels"),
        ("1:1 (Square)", "Total Pixels", 512, 512, 1048576, "1:1 with 1M pixels"),

        # Custom mode tests
        ("Custom", "Width Priority", 768, 1344, 1048576, "Custom 768x1344"),
        ("Custom", "Height Priority", 640, 960, 1048576, "Custom 640x960"),
    ]

    print(f"Running {len(test_cases)} test cases...\n")

    passed = 0
    failed = 0

    for i, (aspect_ratio, dimension_mode, width, height, total_pixels, description) in enumerate(test_cases, 1):
        print(f"Test {i}: {description}")
        print(f"  Input: aspect_ratio='{aspect_ratio}', mode='{dimension_mode}'")
        print(f"         width={width}, height={height}, total_pixels={total_pixels}")

        try:
            # Calculate dimensions
            calc_width, calc_height = node.calculate_dimensions(
                aspect_ratio, dimension_mode, width, height, total_pixels
            )

            print(f"  Output: width={calc_width}, height={calc_height}")

            # Calculate actual aspect ratio
            actual_ratio = calc_width / calc_height
            print(f"  Actual aspect ratio: {actual_ratio:.3f}")

            # Verify dimensions are multiples of 8
            if calc_width % 8 == 0 and calc_height % 8 == 0:
                print("  ✓ Dimensions are multiples of 8")
            else:
                print("  ✗ Dimensions are not multiples of 8!")
                failed += 1
                continue

            # Verify dimensions are reasonable
            if 64 <= calc_width <= 16384 and 64 <= calc_height <= 16384:
                print("  ✓ Dimensions are within valid range")
            else:
                print("  ✗ Dimensions are outside valid range!")
                failed += 1
                continue

            # For non-custom modes, verify aspect ratio is approximately correct
            if aspect_ratio != "Custom":
                expected_ratio = node.ASPECT_RATIOS[aspect_ratio]
                ratio_diff = abs(actual_ratio - expected_ratio)
                if ratio_diff < 0.1:  # Allow some tolerance due to rounding
                    print(f"  ✓ Aspect ratio is close to expected ({expected_ratio:.3f})")
                else:
                    print(f"  ⚠ Aspect ratio differs from expected ({expected_ratio:.3f}) by {ratio_diff:.3f}")

            print("  Status: PASSED")
            passed += 1

        except Exception as e:
            print(f"  ✗ Error: {e}")
            print("  Status: FAILED")
            failed += 1

        print()

    print(f"Testing completed! Passed: {passed}, Failed: {failed}")
    return failed == 0

def test_edge_cases():
    """Test edge cases and boundary conditions."""

    print("\nTesting Edge Cases")
    print("=" * 30)

    node = MockAspectRatioLatentImage()

    test_cases = [
        ("Minimum dimensions", "1:1 (Square)", "Width Priority", 64, 64),
        ("Large dimensions", "16:9 (Widescreen)", "Width Priority", 2048, 1152),
        ("Odd numbers", "16:9 (Widescreen)", "Width Priority", 1023, 575),
        ("Very wide", "21:9 (Ultrawide)", "Width Priority", 1680, 720),
        ("Very tall", "9:21 (Tall Portrait)", "Height Priority", 480, 1120),
    ]

    passed = 0
    failed = 0

    for description, aspect_ratio, dimension_mode, width, height in test_cases:
        print(f"Test: {description}")
        try:
            calc_width, calc_height = node.calculate_dimensions(
                aspect_ratio, dimension_mode, width, height, 1048576
            )
            print(f"  Result: {calc_width}x{calc_height}")

            # Basic validation
            if (calc_width % 8 == 0 and calc_height % 8 == 0 and
                64 <= calc_width <= 16384 and 64 <= calc_height <= 16384):
                print("  Status: PASSED")
                passed += 1
            else:
                print("  Status: FAILED - Invalid dimensions")
                failed += 1
        except Exception as e:
            print(f"  Status: FAILED - {e}")
            failed += 1
        print()

    print(f"Edge case testing completed! Passed: {passed}, Failed: {failed}")
    return failed == 0

if __name__ == "__main__":
    success1 = test_calculations()
    success2 = test_edge_cases()

    if success1 and success2:
        print("\n🎉 All tests passed! The node logic is working correctly.")
    else:
        print("\n❌ Some tests failed. Please review the implementation.")