#!/usr/bin/env python3
"""
Test script for the AspectRatioLatentImage node logic (without PyTorch dependency).
This script tests the calculation logic to ensure the node works correctly.
"""

import math

class MockAspectRatioLatentImage:
    """Mock version of AspectRatioLatentImage for testing logic without PyTorch."""

    ASPECT_RATIOS = {
        "1:1 (Square)": 1.0,
        "4:3 (Standard)": 4/3,
        "3:2 (Classic)": 3/2,
        "16:9 (Widescreen)": 16/9,
        "16:10 (Wide)": 16/10,
        "21:9 (Ultrawide)": 21/9,
        "9:16 (Portrait)": 9/16,
        "3:4 (Portrait)": 3/4,
        "2:3 (Portrait)": 2/3,
        "10:16 (Portrait)": 10/16,
        "9:21 (Tall Portrait)": 9/21,
        "5:4 (Classic)": 5/4,
        "4:5 (Portrait)": 4/5,
        "Custom": None
    }

    def _round_to_multiple(self, value, multiple):
        """Round a value to the nearest multiple."""
        return int(round(value / multiple) * multiple)



    def calculate_dimensions(self, aspect_ratio, width, height, use_width=True):
        """Calculate dimensions based on aspect ratio."""

        # Get the aspect ratio value
        ratio = self.ASPECT_RATIOS.get(aspect_ratio)

        # Calculate final dimensions
        if aspect_ratio == "Custom":
            # Custom mode: use provided width and height as-is
            final_width = self._round_to_multiple(width, 8)
            final_height = self._round_to_multiple(height, 8)
        else:
            # Aspect ratio mode: calculate the other dimension automatically
            if use_width:
                # Use width input, calculate height
                final_width = self._round_to_multiple(width, 8)
                final_height = self._round_to_multiple(int(final_width / ratio), 8)
            else:
                # Use height input, calculate width
                final_height = self._round_to_multiple(height, 8)
                final_width = self._round_to_multiple(int(final_height * ratio), 8)

        # Ensure dimensions are within bounds
        MAX_RESOLUTION = 16384
        final_width = max(64, min(final_width, MAX_RESOLUTION))
        final_height = max(64, min(final_height, MAX_RESOLUTION))

        return final_width, final_height

def test_calculations():
    """Test the dimension calculation logic."""

    print("Testing AspectRatioLatentImage Calculation Logic")
    print("=" * 55)

    node = MockAspectRatioLatentImage()

    # Test cases: (aspect_ratio, width, height, use_width, expected_description)
    test_cases = [
        # Width-based calculations (use_width=True)
        ("16:9 (Widescreen)", 1024, 576, True, "16:9 with width 1024 → height auto-calculated"),
        ("16:9 (Widescreen)", 1280, 720, True, "16:9 with width 1280 → height auto-calculated"),
        ("4:3 (Standard)", 800, 600, True, "4:3 with width 800 → height auto-calculated"),
        ("1:1 (Square)", 512, 512, True, "1:1 with width 512 → height auto-calculated"),
        ("9:16 (Portrait)", 576, 1024, True, "9:16 portrait with width 576 → height auto-calculated"),

        # Height-based calculations (use_width=False)
        ("16:9 (Widescreen)", 1024, 576, False, "16:9 with height 576 → width auto-calculated"),
        ("16:9 (Widescreen)", 1280, 720, False, "16:9 with height 720 → width auto-calculated"),
        ("4:3 (Standard)", 800, 600, False, "4:3 with height 600 → width auto-calculated"),
        ("9:16 (Portrait)", 576, 1024, False, "9:16 portrait with height 1024 → width auto-calculated"),

        # Custom mode tests (both dimensions used as-is)
        ("Custom", 768, 1344, True, "Custom 768x1344 (both dimensions used)"),
        ("Custom", 640, 960, False, "Custom 640x960 (both dimensions used)"),
    ]

    print(f"Running {len(test_cases)} test cases...\n")

    passed = 0
    failed = 0

    for i, (aspect_ratio, width, height, use_width, description) in enumerate(test_cases, 1):
        print(f"Test {i}: {description}")
        print(f"  Input: aspect_ratio='{aspect_ratio}', use_width={use_width}")
        print(f"         width={width}, height={height}")

        try:
            # Calculate dimensions
            calc_width, calc_height = node.calculate_dimensions(
                aspect_ratio, width, height, use_width
            )

            print(f"  Output: width={calc_width}, height={calc_height}")

            # Calculate actual aspect ratio
            actual_ratio = calc_width / calc_height
            print(f"  Actual aspect ratio: {actual_ratio:.3f}")

            # Verify dimensions are multiples of 8
            if calc_width % 8 == 0 and calc_height % 8 == 0:
                print("  ✓ Dimensions are multiples of 8")
            else:
                print("  ✗ Dimensions are not multiples of 8!")
                failed += 1
                continue

            # Verify dimensions are reasonable
            if 64 <= calc_width <= 16384 and 64 <= calc_height <= 16384:
                print("  ✓ Dimensions are within valid range")
            else:
                print("  ✗ Dimensions are outside valid range!")
                failed += 1
                continue

            # For non-custom modes, verify aspect ratio is approximately correct
            if aspect_ratio != "Custom":
                expected_ratio = node.ASPECT_RATIOS[aspect_ratio]
                ratio_diff = abs(actual_ratio - expected_ratio)
                if ratio_diff < 0.1:  # Allow some tolerance due to rounding
                    print(f"  ✓ Aspect ratio is close to expected ({expected_ratio:.3f})")
                else:
                    print(f"  ⚠ Aspect ratio differs from expected ({expected_ratio:.3f}) by {ratio_diff:.3f}")

            print("  Status: PASSED")
            passed += 1

        except Exception as e:
            print(f"  ✗ Error: {e}")
            print("  Status: FAILED")
            failed += 1

        print()

    print(f"Testing completed! Passed: {passed}, Failed: {failed}")
    return failed == 0

def test_edge_cases():
    """Test edge cases and boundary conditions."""

    print("\nTesting Edge Cases")
    print("=" * 30)

    node = MockAspectRatioLatentImage()

    test_cases = [
        ("Minimum dimensions", "1:1 (Square)", 64, 64, True),
        ("Large dimensions", "16:9 (Widescreen)", 2048, 1152, True),
        ("Odd numbers", "16:9 (Widescreen)", 1023, 575, True),
        ("Very wide", "21:9 (Ultrawide)", 1680, 720, True),
        ("Very tall", "9:21 (Tall Portrait)", 480, 1120, False),
    ]

    passed = 0
    failed = 0

    for description, aspect_ratio, width, height, use_width in test_cases:
        print(f"Test: {description}")
        try:
            calc_width, calc_height = node.calculate_dimensions(
                aspect_ratio, width, height, use_width
            )
            print(f"  Result: {calc_width}x{calc_height}")

            # Basic validation
            if (calc_width % 8 == 0 and calc_height % 8 == 0 and
                64 <= calc_width <= 16384 and 64 <= calc_height <= 16384):
                print("  Status: PASSED")
                passed += 1
            else:
                print("  Status: FAILED - Invalid dimensions")
                failed += 1
        except Exception as e:
            print(f"  Status: FAILED - {e}")
            failed += 1
        print()

    print(f"Edge case testing completed! Passed: {passed}, Failed: {failed}")
    return failed == 0

if __name__ == "__main__":
    success1 = test_calculations()
    success2 = test_edge_cases()

    if success1 and success2:
        print("\n🎉 All tests passed! The node logic is working correctly.")
    else:
        print("\n❌ Some tests failed. Please review the implementation.")