"""
Aspect Ratio Latent Image Node for ComfyUI

This node provides automatic dimension calculation based on aspect ratios,
similar to Empty Latent Image but with enhanced aspect ratio functionality.
"""

import torch
import math

# Maximum resolution constant (same as ComfyUI)
MAX_RESOLUTION = 16384

class AspectRatioLatentImage:
    """
    A ComfyUI node that creates empty latent images with automatic dimension calculation
    based on aspect ratios. Supports both preset aspect ratios and free scaling.
    """

    # Common aspect ratios with their decimal values
    ASPECT_RATIOS = {
        "1:1 (Square)": 1.0,
        "4:3 (Standard)": 4/3,
        "3:2 (Classic)": 3/2,
        "16:9 (Widescreen)": 16/9,
        "16:10 (Wide)": 16/10,
        "21:9 (Ultrawide)": 21/9,
        "9:16 (Portrait)": 9/16,
        "3:4 (Portrait)": 3/4,
        "2:3 (Portrait)": 2/3,
        "10:16 (Portrait)": 10/16,
        "9:21 (Tall Portrait)": 9/21,
        "5:4 (Classic)": 5/4,
        "4:5 (Portrait)": 4/5,
        "Custom": None  # For free scaling
    }

    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "aspect_ratio": (list(cls.ASPECT_RATIOS.keys()), {
                    "default": "16:9 (Widescreen)",
                    "tooltip": "Select an aspect ratio preset or 'Custom' for manual width/height"
                }),
                "width": ("INT", {
                    "default": 1024,
                    "min": 64,
                    "max": MAX_RESOLUTION,
                    "step": 8,
                    "tooltip": "Width in pixels - height will be auto-calculated based on aspect ratio"
                }),
                "height": ("INT", {
                    "default": 576,
                    "min": 64,
                    "max": MAX_RESOLUTION,
                    "step": 8,
                    "tooltip": "Height in pixels - width will be auto-calculated based on aspect ratio"
                }),
                "batch_size": ("INT", {
                    "default": 1,
                    "min": 1,
                    "max": 4096,
                    "tooltip": "Number of latent images to generate"
                }),
            },
            "optional": {
                "use_width": ("BOOLEAN", {
                    "default": True,
                    "tooltip": "If True, use width input and calculate height. If False, use height input and calculate width."
                }),
            }
        }

    RETURN_TYPES = ("LATENT", "INT", "INT")
    RETURN_NAMES = ("latent", "width", "height")
    OUTPUT_TOOLTIPS = (
        "The generated empty latent image",
        "The calculated width in pixels",
        "The calculated height in pixels"
    )
    FUNCTION = "generate"
    CATEGORY = "latent"
    DESCRIPTION = "Creates empty latent images with automatic dimension calculation based on aspect ratios. Input width or height, the other dimension is calculated automatically."

    def generate(self, aspect_ratio, width, height, batch_size, use_width=True):
        """
        Generate an empty latent image with calculated dimensions based on aspect ratio.

        Args:
            aspect_ratio: Selected aspect ratio preset or "Custom"
            width: Input width value
            height: Input height value
            batch_size: Number of latent images to generate
            use_width: If True, use width and calculate height. If False, use height and calculate width.

        Returns:
            Tuple of (latent_dict, calculated_width, calculated_height)
        """

        # Get the aspect ratio value
        ratio = self.ASPECT_RATIOS.get(aspect_ratio)

        # Calculate final dimensions
        if aspect_ratio == "Custom":
            # Custom mode: use provided width and height as-is
            final_width = self._round_to_multiple(width, 8)
            final_height = self._round_to_multiple(height, 8)
        else:
            # Aspect ratio mode: calculate the other dimension automatically
            if use_width:
                # Use width input, calculate height
                final_width = self._round_to_multiple(width, 8)
                final_height = self._round_to_multiple(int(final_width / ratio), 8)
            else:
                # Use height input, calculate width
                final_height = self._round_to_multiple(height, 8)
                final_width = self._round_to_multiple(int(final_height * ratio), 8)

        # Ensure dimensions are within bounds
        final_width = max(64, min(final_width, MAX_RESOLUTION))
        final_height = max(64, min(final_height, MAX_RESOLUTION))

        # Create the latent tensor (4 channels for latent space, divided by 8 for VAE scaling)
        latent = torch.zeros([
            batch_size,
            4,
            final_height // 8,
            final_width // 8
        ])

        return ({"samples": latent}, final_width, final_height)

    def _round_to_multiple(self, value, multiple):
        """Round a value to the nearest multiple."""
        return int(round(value / multiple) * multiple)




# Node registration for ComfyUI
NODE_CLASS_MAPPINGS = {
    "AspectRatioLatentImage": AspectRatioLatentImage
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "AspectRatioLatentImage": "Aspect Ratio Latent Image"
}