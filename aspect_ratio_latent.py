"""
Aspect Ratio Latent Image Node for ComfyUI

This node provides automatic dimension calculation based on aspect ratios,
similar to Empty Latent Image but with enhanced aspect ratio functionality.
"""

import torch
import math

# Maximum resolution constant (same as ComfyUI)
MAX_RESOLUTION = 16384

class AspectRatioLatentImage:
    """
    A ComfyUI node that creates empty latent images with automatic dimension calculation
    based on aspect ratios. Supports both preset aspect ratios and free scaling.
    """

    # Common aspect ratios with their decimal values
    ASPECT_RATIOS = {
        "1:1 (Square)": 1.0,
        "4:3 (Standard)": 4/3,
        "3:2 (Classic)": 3/2,
        "16:9 (Widescreen)": 16/9,
        "16:10 (Wide)": 16/10,
        "21:9 (Ultrawide)": 21/9,
        "9:16 (Portrait)": 9/16,
        "3:4 (Portrait)": 3/4,
        "2:3 (Portrait)": 2/3,
        "10:16 (Portrait)": 10/16,
        "9:21 (Tall Portrait)": 9/21,
        "5:4 (Classic)": 5/4,
        "4:5 (Portrait)": 4/5,
        "Custom": None  # For free scaling
    }

    def __init__(self):
        pass

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "aspect_ratio": (list(cls.ASPECT_RATIOS.keys()), {
                    "default": "16:9 (Widescreen)",
                    "tooltip": "Select an aspect ratio preset or 'Custom' for free scaling"
                }),
                "dimension_mode": (["Width Priority", "Height Priority", "Total Pixels"], {
                    "default": "Width Priority",
                    "tooltip": "How to calculate dimensions: prioritize width, height, or total pixel count"
                }),
                "width": ("INT", {
                    "default": 1024,
                    "min": 64,
                    "max": MAX_RESOLUTION,
                    "step": 8,
                    "tooltip": "Width in pixels (used as input or output depending on mode)"
                }),
                "height": ("INT", {
                    "default": 576,
                    "min": 64,
                    "max": MAX_RESOLUTION,
                    "step": 8,
                    "tooltip": "Height in pixels (used as input or output depending on mode)"
                }),
                "total_pixels": ("INT", {
                    "default": 1048576,  # 1024x1024
                    "min": 65536,        # 256x256
                    "max": 4194304,      # 2048x2048
                    "step": 65536,
                    "tooltip": "Total pixel count (width × height) when using Total Pixels mode"
                }),
                "batch_size": ("INT", {
                    "default": 1,
                    "min": 1,
                    "max": 4096,
                    "tooltip": "Number of latent images to generate"
                }),
            }
        }

    RETURN_TYPES = ("LATENT", "INT", "INT")
    RETURN_NAMES = ("latent", "width", "height")
    OUTPUT_TOOLTIPS = (
        "The generated empty latent image",
        "The calculated width in pixels",
        "The calculated height in pixels"
    )
    FUNCTION = "generate"
    CATEGORY = "latent"
    DESCRIPTION = "Creates empty latent images with automatic dimension calculation based on aspect ratios. Supports preset ratios and custom scaling modes."

    def generate(self, aspect_ratio, dimension_mode, width, height, total_pixels, batch_size):
        """
        Generate an empty latent image with calculated dimensions based on aspect ratio.

        Args:
            aspect_ratio: Selected aspect ratio preset or "Custom"
            dimension_mode: How to calculate dimensions
            width: Input width value
            height: Input height value
            total_pixels: Target total pixel count
            batch_size: Number of latent images to generate

        Returns:
            Tuple of (latent_dict, calculated_width, calculated_height)
        """

        # Get the aspect ratio value
        ratio = self.ASPECT_RATIOS.get(aspect_ratio)

        # Calculate final dimensions based on mode
        if aspect_ratio == "Custom":
            # Custom mode: use provided width and height as-is
            final_width = self._round_to_multiple(width, 8)
            final_height = self._round_to_multiple(height, 8)
        else:
            # Aspect ratio mode: calculate based on selected mode
            if dimension_mode == "Width Priority":
                final_width = self._round_to_multiple(width, 8)
                final_height = self._round_to_multiple(int(final_width / ratio), 8)
            elif dimension_mode == "Height Priority":
                final_height = self._round_to_multiple(height, 8)
                final_width = self._round_to_multiple(int(final_height * ratio), 8)
            else:  # Total Pixels mode
                final_width, final_height = self._calculate_from_total_pixels(total_pixels, ratio)

        # Ensure dimensions are within bounds
        final_width = max(64, min(final_width, MAX_RESOLUTION))
        final_height = max(64, min(final_height, MAX_RESOLUTION))

        # Create the latent tensor (4 channels for latent space, divided by 8 for VAE scaling)
        latent = torch.zeros([
            batch_size,
            4,
            final_height // 8,
            final_width // 8
        ])

        return ({"samples": latent}, final_width, final_height)

    def _round_to_multiple(self, value, multiple):
        """Round a value to the nearest multiple."""
        return int(round(value / multiple) * multiple)

    def _calculate_from_total_pixels(self, total_pixels, ratio):
        """Calculate width and height from total pixels and aspect ratio."""
        # total_pixels = width * height
        # ratio = width / height
        # So: width = sqrt(total_pixels * ratio), height = sqrt(total_pixels / ratio)

        width = math.sqrt(total_pixels * ratio)
        height = math.sqrt(total_pixels / ratio)

        # Round to multiples of 8
        width = self._round_to_multiple(width, 8)
        height = self._round_to_multiple(height, 8)

        return width, height


# Node registration for ComfyUI
NODE_CLASS_MAPPINGS = {
    "AspectRatioLatentImage": AspectRatioLatentImage
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "AspectRatioLatentImage": "Aspect Ratio Latent Image"
}