# ComfyUI Aspect Ratio Latent Image Node

A custom node for ComfyUI that automatically calculates dimensions based on aspect ratios, similar to the Empty Latent Image node but with enhanced aspect ratio functionality.

## Features

- **Preset Aspect Ratios**: Choose from common aspect ratios like 16:9, 4:3, 1:1, and more
- **Automatic Calculation**:
  - Input width → height is automatically calculated
  - Input height → width is automatically calculated
  - Toggle between width-based or height-based calculation
- **Custom Mode**: Free scaling with manual width and height input
- **Portrait and Landscape**: Supports both orientations
- **Automatic Rounding**: Ensures dimensions are multiples of 8 (required for VAE)
- **Dimension Output**: Returns calculated width and height as separate outputs

## Installation

1. Copy this entire folder to your ComfyUI `custom_nodes` directory
2. Restart ComfyUI
3. The node will appear in the `latent` category as "Aspect Ratio Latent Image"

## Usage

### Basic Usage

1. Add the "Aspect Ratio Latent Image" node to your workflow
2. Select an aspect ratio from the dropdown (e.g., "16:9 (Widescreen)")
3. Input either width or height:
   - **Use Width = True**: Input width, height will be auto-calculated
   - **Use Width = False**: Input height, width will be auto-calculated
4. Connect the latent output to your sampler

### Available Aspect Ratios

- **1:1 (Square)** - Perfect square format
- **4:3 (Standard)** - Classic TV/monitor ratio
- **3:2 (Classic)** - Traditional photography ratio
- **16:9 (Widescreen)** - Modern widescreen format
- **16:10 (Wide)** - Computer monitor ratio
- **21:9 (Ultrawide)** - Ultrawide monitor ratio
- **9:16 (Portrait)** - Vertical phone/social media
- **3:4 (Portrait)** - Vertical standard ratio
- **2:3 (Portrait)** - Vertical photography ratio
- **10:16 (Portrait)** - Vertical wide ratio
- **9:21 (Tall Portrait)** - Very tall vertical ratio
- **5:4 (Classic)** - Classic square-ish ratio
- **4:5 (Portrait)** - Vertical square-ish ratio
- **Custom** - Manual width and height input

### Examples

#### Example 1: 16:9 Widescreen with Width Input
- Aspect Ratio: "16:9 (Widescreen)"
- Use Width: True
- Width: 1280
- Result: 1280×720 latent image (height auto-calculated)

#### Example 2: 16:9 Widescreen with Height Input
- Aspect Ratio: "16:9 (Widescreen)"
- Use Width: False
- Height: 720
- Result: 1280×720 latent image (width auto-calculated)

#### Example 3: Portrait Mode with Width Input
- Aspect Ratio: "9:16 (Portrait)"
- Use Width: True
- Width: 576
- Result: 576×1024 latent image (height auto-calculated)

#### Example 4: Custom Free Scaling
- Aspect Ratio: "Custom"
- Width: 768
- Height: 1344
- Result: 768×1344 latent image (both dimensions used as-is)

## Node Outputs

The node provides three outputs:

1. **LATENT**: The generated empty latent image tensor
2. **WIDTH**: The calculated width in pixels (INT)
3. **HEIGHT**: The calculated height in pixels (INT)

The width and height outputs can be useful for:
- Connecting to other nodes that need dimension information
- Debugging and verification
- Chaining with other custom nodes

## Technical Details

- All dimensions are automatically rounded to multiples of 8 (VAE requirement)
- Latent tensors have 4 channels and are downscaled by 8 (standard VAE scaling)
- Maximum resolution is 16384 pixels (same as ComfyUI default)
- Minimum resolution is 64 pixels per dimension
- Supports batch sizes up to 4096

## Workflow Integration

This node is designed to be a drop-in replacement for the standard Empty Latent Image node. Simply:

1. Replace your Empty Latent Image node with this one
2. Select your desired aspect ratio
3. Choose the appropriate dimension mode
4. Set your preferred dimension value

The rest of your workflow (sampler, VAE decode, etc.) remains unchanged.

## Troubleshooting

**Q: The calculated dimensions don't match exactly what I expected**
A: Dimensions are automatically rounded to multiples of 8, which may cause slight variations from exact aspect ratios.

**Q: Can I use this with any model?**
A: Yes, this node generates standard latent tensors compatible with all Stable Diffusion models.

**Q: What's the difference between this and Empty Latent Image?**
A: This node adds automatic aspect ratio calculation and multiple input modes, while maintaining full compatibility with the standard node.

## License

This project is released under the same license as ComfyUI.