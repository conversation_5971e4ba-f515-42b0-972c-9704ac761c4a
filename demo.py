#!/usr/bin/env python3
"""
Demo script showing how the AspectRatioLatentImage node works.
This demonstrates the automatic calculation feature.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_logic import MockAspectRatioLatentImage

def demo_automatic_calculation():
    """Demonstrate the automatic dimension calculation."""

    print("🎯 AspectRatioLatentImage Node - Automatic Calculation Demo")
    print("=" * 60)

    node = MockAspectRatioLatentImage()

    print("\n📐 Example: 16:9 Aspect Ratio")
    print("-" * 30)

    # Example 1: Input width, get height
    print("Input width = 1280, calculate height:")
    width, height = node.calculate_dimensions("16:9 (Widescreen)", 1280, 720, use_width=True)
    print(f"  → Result: {width} × {height}")
    print(f"  → Aspect ratio: {width/height:.3f} (expected: {16/9:.3f})")

    # Example 2: Input height, get width
    print("\nInput height = 720, calculate width:")
    width, height = node.calculate_dimensions("16:9 (Widescreen)", 1280, 720, use_width=False)
    print(f"  → Result: {width} × {height}")
    print(f"  → Aspect ratio: {width/height:.3f} (expected: {16/9:.3f})")

    print("\n📱 Example: 9:16 Portrait Aspect Ratio")
    print("-" * 40)

    # Example 3: Portrait with width input
    print("Input width = 576, calculate height:")
    width, height = node.calculate_dimensions("9:16 (Portrait)", 576, 1024, use_width=True)
    print(f"  → Result: {width} × {height}")
    print(f"  → Aspect ratio: {width/height:.3f} (expected: {9/16:.3f})")

    # Example 4: Portrait with height input
    print("\nInput height = 1024, calculate width:")
    width, height = node.calculate_dimensions("9:16 (Portrait)", 576, 1024, use_width=False)
    print(f"  → Result: {width} × {height}")
    print(f"  → Aspect ratio: {width/height:.3f} (expected: {9/16:.3f})")

    print("\n🎨 Example: Custom Mode")
    print("-" * 25)

    # Example 5: Custom mode (both dimensions used)
    print("Custom mode - both dimensions used as-is:")
    width, height = node.calculate_dimensions("Custom", 768, 1344, use_width=True)
    print(f"  → Result: {width} × {height}")
    print(f"  → Aspect ratio: {width/height:.3f} (custom ratio)")

    print("\n✨ Key Benefits:")
    print("  • No need to select calculation mode")
    print("  • Just input one dimension, the other is calculated automatically")
    print("  • Toggle 'use_width' to switch between width-based or height-based calculation")
    print("  • Perfect for quick aspect ratio adjustments")

    print("\n🔧 In ComfyUI:")
    print("  1. Select aspect ratio (e.g., '16:9 (Widescreen)')")
    print("  2. Set 'Use Width' to True or False")
    print("  3. Input either width or height")
    print("  4. The other dimension is calculated automatically!")

if __name__ == "__main__":
    demo_automatic_calculation()
