#!/usr/bin/env python3
"""
Test script for the AspectRatioLatentImage node.
This script tests various aspect ratios and calculation modes to ensure the node works correctly.
"""

import sys
import os

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from aspect_ratio_latent import AspectRatioLatentImage

def test_aspect_ratio_calculations():
    """Test the aspect ratio calculations with various inputs."""

    print("Testing AspectRatioLatentImage Node")
    print("=" * 50)

    # Create an instance of the node
    node = AspectRatioLatentImage()

    # Test cases: (aspect_ratio, dimension_mode, width, height, total_pixels, expected_description)
    test_cases = [
        # Width Priority tests
        ("16:9 (Widescreen)", "Width Priority", 1024, 576, 1048576, "16:9 with width 1024"),
        ("4:3 (Standard)", "Width Priority", 800, 600, 1048576, "4:3 with width 800"),
        ("1:1 (Square)", "Width Priority", 512, 512, 1048576, "1:1 with width 512"),
        ("9:16 (Portrait)", "Width Priority", 576, 1024, 1048576, "9:16 portrait with width 576"),

        # Height Priority tests
        ("16:9 (Widescreen)", "Height Priority", 1024, 576, 1048576, "16:9 with height 576"),
        ("4:3 (Standard)", "Height Priority", 800, 600, 1048576, "4:3 with height 600"),
        ("9:16 (Portrait)", "Height Priority", 576, 1024, 1048576, "9:16 portrait with height 1024"),

        # Total Pixels tests
        ("16:9 (Widescreen)", "Total Pixels", 1024, 576, 1048576, "16:9 with 1M pixels"),
        ("1:1 (Square)", "Total Pixels", 512, 512, 1048576, "1:1 with 1M pixels"),

        # Custom mode tests
        ("Custom", "Width Priority", 768, 1344, 1048576, "Custom 768x1344"),
        ("Custom", "Height Priority", 640, 960, 1048576, "Custom 640x960"),
    ]

    print(f"Running {len(test_cases)} test cases...\n")

    for i, (aspect_ratio, dimension_mode, width, height, total_pixels, description) in enumerate(test_cases, 1):
        print(f"Test {i}: {description}")
        print(f"  Input: aspect_ratio='{aspect_ratio}', mode='{dimension_mode}'")
        print(f"         width={width}, height={height}, total_pixels={total_pixels}")

        try:
            # Call the generate method
            result = node.generate(
                aspect_ratio=aspect_ratio,
                dimension_mode=dimension_mode,
                width=width,
                height=height,
                total_pixels=total_pixels,
                batch_size=1
            )

            # Unpack the results
            latent_dict, calc_width, calc_height = result
            latent_tensor = latent_dict["samples"]

            # Verify the latent tensor shape
            expected_latent_height = calc_height // 8
            expected_latent_width = calc_width // 8

            print(f"  Output: width={calc_width}, height={calc_height}")
            print(f"  Latent shape: {list(latent_tensor.shape)} (expected: [1, 4, {expected_latent_height}, {expected_latent_width}])")

            # Calculate actual aspect ratio
            actual_ratio = calc_width / calc_height
            print(f"  Actual aspect ratio: {actual_ratio:.3f}")

            # Verify latent tensor shape is correct
            if list(latent_tensor.shape) == [1, 4, expected_latent_height, expected_latent_width]:
                print("  ✓ Latent tensor shape is correct")
            else:
                print("  ✗ Latent tensor shape is incorrect!")

            # Verify dimensions are multiples of 8
            if calc_width % 8 == 0 and calc_height % 8 == 0:
                print("  ✓ Dimensions are multiples of 8")
            else:
                print("  ✗ Dimensions are not multiples of 8!")

            print("  Status: PASSED")

        except Exception as e:
            print(f"  ✗ Error: {e}")
            print("  Status: FAILED")

        print()

    print("Testing completed!")

def test_edge_cases():
    """Test edge cases and boundary conditions."""

    print("\nTesting Edge Cases")
    print("=" * 30)

    node = AspectRatioLatentImage()

    # Test minimum dimensions
    print("Test: Minimum dimensions")
    try:
        result = node.generate("1:1 (Square)", "Width Priority", 64, 64, 1048576, 1)
        latent_dict, calc_width, calc_height = result
        print(f"  Minimum test: {calc_width}x{calc_height} - PASSED")
    except Exception as e:
        print(f"  Minimum test failed: {e}")

    # Test large dimensions
    print("Test: Large dimensions")
    try:
        result = node.generate("16:9 (Widescreen)", "Width Priority", 2048, 1152, 1048576, 1)
        latent_dict, calc_width, calc_height = result
        print(f"  Large test: {calc_width}x{calc_height} - PASSED")
    except Exception as e:
        print(f"  Large test failed: {e}")

    # Test batch size
    print("Test: Batch size")
    try:
        result = node.generate("1:1 (Square)", "Width Priority", 512, 512, 1048576, 4)
        latent_dict, calc_width, calc_height = result
        latent_tensor = latent_dict["samples"]
        expected_shape = [4, 4, 64, 64]  # batch_size=4, channels=4, height=512//8, width=512//8
        if list(latent_tensor.shape) == expected_shape:
            print(f"  Batch test: shape {list(latent_tensor.shape)} - PASSED")
        else:
            print(f"  Batch test: expected {expected_shape}, got {list(latent_tensor.shape)} - FAILED")
    except Exception as e:
        print(f"  Batch test failed: {e}")

if __name__ == "__main__":
    test_aspect_ratio_calculations()
    test_edge_cases()
    print("\nAll tests completed!")